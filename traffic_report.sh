#!/bin/bash

# Calculate timestamp for 24 hours ago
timestamp_24h_ago=$(date -d "24 hours ago" "+%d/%b/%Y:%H:%M:%S")

# Create a temporary file with only entries from the last 24 hours
echo "Filtering logs for entries from the last 24 hours..."
sudo awk -v cutoff="$timestamp_24h_ago" '
function parse_time(ts) {
  # Remove brackets from timestamp field
  gsub(/[\[\]]/, "", ts)
  return ts
}
{
  # Extract timestamp (4th field)
  log_time = parse_time($4)
  
  # If log time is greater than or equal to cutoff time, print the line
  if (log_time >= cutoff) {
    print $0
  }
}' /var/log/nginx/access.log /var/log/nginx/marketsignals_access.log > /tmp/last_24h_nginx.log 2>/dev/null
./
# Generate comprehensive Nginx traffic analysis report with focus on indicator paths
echo -e "NGINX TRAFFIC ANALYSIS REPORT - LAST 24 HOURS\n" > /tmp/nginx_report.txt 

# Part 1: Top IPs by request count
echo -e "1. TOP 20 IPs BY REQUEST COUNT:\n" >> /tmp/nginx_report.txt 
cat /tmp/last_24h_nginx.log | awk '{print $1}' | sort | uniq -c | sort -nr | head -20 >> /tmp/nginx_report.txt 

# Part 2: Indicator API requests analysis
echo -e "\n\n2. INDICATOR API REQUESTS:\n" >> /tmp/nginx_report.txt
echo -e "2.1 STOCK INDICATORS BY SYMBOL:\n" >> /tmp/nginx_report.txt
grep -i "stock/indicators" /tmp/last_24h_nginx.log 2>/dev/null | awk -F'symbol=' '{print $2}' | awk -F'&' '{print $1}' | sort | uniq -c | sort -nr >> /tmp/nginx_report.txt

echo -e "\n2.2 CRYPTO INDICATORS BY SYMBOL:\n" >> /tmp/nginx_report.txt
grep -i "crypto/indicators" /tmp/last_24h_nginx.log 2>/dev/null | awk -F'symbol=' '{print $2}' | awk -F'&' '{print $1}' | sort | uniq -c | sort -nr >> /tmp/nginx_report.txt

echo -e "\n2.3 INDICATORS BY CONVERSION CURRENCY:\n" >> /tmp/nginx_report.txt
grep -i "indicators" /tmp/last_24h_nginx.log 2>/dev/null | awk -F'conversionCurrency=' '{print $2}' | awk -F'\"' '{print $1}' | sort | uniq -c | sort -nr >> /tmp/nginx_report.txt

echo -e "\n2.4 TOP IPs REQUESTING INDICATORS:\n" >> /tmp/nginx_report.txt
grep -i "indicators" /tmp/last_24h_nginx.log 2>/dev/null | awk '{print $1}' | sort | uniq -c | sort -nr | head -10 >> /tmp/nginx_report.txt

echo -e "\n2.5 DETAILED IP MAPPING TO INDICATORS:\n" >> /tmp/nginx_report.txt

# Extract the IPs first
echo -e "=== MAPPING OF IP ADDRESSES TO INDICATORS ===\n" >> /tmp/nginx_report.txt
echo -e "FORMAT: COUNT IP SYMBOL/CURRENCY\n" >> /tmp/nginx_report.txt

# Process log files and extract IP address, symbol and currency
grep -i "indicators" /tmp/last_24h_nginx.log 2>/dev/null | 
  awk '{
    # Clean IP address by removing log file prefix if present
    ip=$1;
    
    # Extract request path including query parameters
    for(i=1; i<=NF; i++) {
      if($i ~ /GET/ || $i ~ /POST/) {
        req=$(i+1);
        break;
      }
    }
    
    # Extract symbol and currency from request
    if(match(req, /symbol=([^&]+)/, symbol) && match(req, /conversionCurrency=([^&" ]+)/, currency)) {
      # Clean up the extracted values
      sym = symbol[1];
      curr = currency[1];
      # Print in format that makes it easy to group by IP
      print ip, sym"/"curr;
    }
  }' | sort | uniq -c | sort -k2,2 -k1,1nr >> /tmp/nginx_report.txt

# Add timestamp details for each indicator request
echo -e "\n=== CHRONOLOGICAL INDICATOR REQUEST DETAILS ===\n" >> /tmp/nginx_report.txt
echo -e "FORMAT: TIMESTAMP | IP | SYMBOL | CURRENCY\n" >> /tmp/nginx_report.txt

# Sort by timestamp to see chronological order
grep -i "indicators" /tmp/last_24h_nginx.log 2>/dev/null | 
  awk '{
    # Clean IP address
    ip=$1;
    
    # Extract timestamp from the 4th field
    ts=$4;
    gsub(/[\[\]]/, "", ts);
    
    # Extract request path including query parameters
    for(i=1; i<=NF; i++) {
      if($i ~ /GET/ || $i ~ /POST/) {
        req=$(i+1);
        break;
      }
    }
    
    # Extract symbol and currency from request
    if(match(req, /symbol=([^&]+)/, symbol) && match(req, /conversionCurrency=([^&" ]+)/, currency)) {
      # Clean up the extracted values
      sym = symbol[1];
      curr = currency[1];
      # Print in format with timestamp
      printf "%s | %s | %s | %s\n", ts, ip, sym, curr;
    }
  }' | sort >> /tmp/nginx_report.txt
  
# Add summary of unique indicators per IP with cleaner formatting
echo -e "\n=== SUMMARY OF UNIQUE INDICATORS PER IP ===\n" >> /tmp/nginx_report.txt
echo -e "FORMAT: IP ADDRESS | NUMBER OF UNIQUE SYMBOLS REQUESTED\n" >> /tmp/nginx_report.txt

# Get a list of unique IPs from the logs to avoid duplication
grep -i "indicators" /tmp/last_24h_nginx.log 2>/dev/null | 
  awk '{print $1}' | sort | uniq > /tmp/indicator_ips.txt

# Create a list of unique clean IP addresses
grep -i "indicators" /tmp/last_24h_nginx.log 2>/dev/null | awk '{print $1}' | sort | uniq > /tmp/indicator_ips_clean.txt

# For each unique clean IP, count the number of unique symbols
while read clean_ip; do
  # Count unique symbols requested by this IP
  unique_count=$(grep -i "indicators" /tmp/last_24h_nginx.log 2>/dev/null | 
    grep "$clean_ip " | awk -F'symbol=' '{print $2}' | awk -F'&' '{print $1}' | sort -u | wc -l)
  echo "$unique_count $clean_ip" 
done < /tmp/indicator_ips_clean.txt | sort -nr | 
  awk '{printf "%-16s | %4d unique symbols requested\n", $2, $1}' >> /tmp/nginx_report.txt

# Add top indicators requested by each IP with clean IP display
echo -e "\n=== TOP 5 INDICATORS REQUESTED BY EACH IP ===\n" >> /tmp/nginx_report.txt

while read clean_ip; do
  echo -e "\nIP: $clean_ip\n" >> /tmp/nginx_report.txt
  
  # Get total requests for this IP
  total=$(grep -i "indicators" /tmp/last_24h_nginx.log 2>/dev/null | grep "$clean_ip " | wc -l)
  echo "  Total indicator requests: $total" >> /tmp/nginx_report.txt
  
  # Show top 5 requested symbols
  grep -i "indicators" /tmp/last_24h_nginx.log 2>/dev/null | 
    grep "$clean_ip " | awk -F'symbol=' '{print $2}' | awk -F'&' '{print $1}' | sort | uniq -c | sort -nr | head -5 | 
    awk '{printf "  %3d requests for %s\n", $1, $2}' >> /tmp/nginx_report.txt
done < /tmp/indicator_ips_clean.txt

# Part 3: HTTP status code distribution for indicator requests
echo -e "\n\n3. HTTP STATUS CODES FOR INDICATOR REQUESTS:\n" >> /tmp/nginx_report.txt 
grep -i "indicators" /tmp/last_24h_nginx.log 2>/dev/null | awk '{print $9}' | sort | uniq -c | sort -nr >> /tmp/nginx_report.txt 

# Clean up temporary files
rm -f /tmp/last_24h_nginx.log /tmp/indicator_ips.txt /tmp/indicator_ips_clean.txt

# Display the full report
cat /tmp/nginx_report.txt