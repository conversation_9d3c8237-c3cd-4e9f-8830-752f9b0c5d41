import React from "react";

import { useChart } from "@/hooks/useChart";

import type { ChartContainerProps } from "@/types/chart";

export const ChartContainer: React.FC<ChartContainerProps> = ({
  data
}) => {
  const { containerRef } = useChart(data);

  return (
    <div className="chart-container">
      <div
        ref={containerRef as React.RefObject<HTMLDivElement>}
        className="chart-wrapper"
        style={{ width: "100%", height: "100%" }}
      />
    </div>
  );
};
