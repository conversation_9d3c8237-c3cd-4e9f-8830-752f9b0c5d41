import React from 'react';

import { Card } from '@/components/card/Card';
import { FilterDrawer } from '@/components/filters/FilterDrawer';
import { FilterToggle } from '@/components/filters/FilterToggle';

import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockStatisticsDto,
} from '@/generated';
import type { FilterConfig, StockFilterConfig } from '@/types/table';

interface MobileLayoutProps {
  data: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
  filterConfig: FilterConfig | StockFilterConfig;
  onUsdSignalChange: (
    signal: FilterConfig['usdSignal'] | StockFilterConfig['usdSignal'],
  ) => void;
  onBtcSignalChange: (signal: FilterConfig['btcSignal']) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
  isFilterDrawerOpen: boolean;
  onFilterDrawerToggle: (open: boolean) => void;
  assetType?: 'crypto' | 'stock';
}

// Helper component for rendering cards
const CardsContainer: React.FC<{
  data: (CryptoCurrencyStatisticsDto | StockStatisticsDto)[];
  btcStatistics: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
  assetType: 'crypto' | 'stock';
}> = ({
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  assetType,
}) => (
  <div className="crypto-cards-container">
    {data.map(item => {
      const btcData = findBtcDataForSymbol(btcStatistics, item.symbol);
      return (
        <Card
          key={item.symbol}
          crypto={item as CryptoCurrencyStatisticsDto}
          {...(btcData && { btcData })}
          onSignalClick={onSignalClick}
          formatDate={formatDate}
          assetType={assetType}
        />
      );
    })}
  </div>
);

// eslint-disable-next-line max-lines-per-function
export const MobileLayout: React.FC<MobileLayoutProps> = ({
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  filterConfig,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
  isFilterDrawerOpen,
  onFilterDrawerToggle,
  assetType = 'crypto',
}) => {
  return (
    <div className="mobile-layout">
      <FilterToggle
        onClick={() => {
          onFilterDrawerToggle(true);
        }}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
      />

      <CardsContainer
        data={data}
        btcStatistics={btcStatistics}
        onSignalClick={onSignalClick}
        formatDate={formatDate}
        findBtcDataForSymbol={findBtcDataForSymbol}
        assetType={assetType}
      />

      <FilterDrawer
        isOpen={isFilterDrawerOpen}
        onClose={() => {
          onFilterDrawerToggle(false);
        }}
        filterConfig={filterConfig}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
        assetType={assetType}
      />
    </div>
  );
};
