export const TIMING = {
  DATA_REFRESH_INTERVAL: 30_000,
  CHART_ANIMATION_DURATION: 300,
} as const;

export const CURRENCIES = {
  USD: 'USD',
  BTC: 'BTC',
} as const;

export const UI_TEXT = {
  APP_TITLE: '⚡ Market Signal Hub',
  APP_SUBTITLE: 'Cryptocurrency and Stock Signals',
  LOADING_CRYPTO_DATA: 'Loading data...',
  LOADING_CHART_DATA: 'Loading chart data...',
  REFRESHING: 'Refreshing...',
  REFRESH: 'Refresh',
  CLICK_TO_VIEW_CHART: 'Click to view chart',
  CRYPTOCURRENCIES: 'cryptocurrencies',
} as const;

export const TABLE_HEADERS = {
  CRYPTOCURRENCY: 'Cryptocurrency',
  USD_PRICE: 'USD Price',
  MARKET_CAP: 'Market Cap',
  LAST_UPDATE: 'Last Update',
  USD_SIGNAL: 'USD Signal',
  SMMA_29_USD: 'SMMA-29 (USD)',
  BTC_PRICE: 'BTC Price',
  BTC_SIGNAL: 'BTC Signal',
  SMMA_29_BTC: 'SMMA-29 (BTC)',
} as const;

export const STOCK_TABLE_HEADERS = {
  STOCK: 'Stock',
  PRICE: 'Price',
  VOLUME: 'Volume',
  SIGNAL: 'Signal',
  BTC_PRICE: 'BTC Price',
  BTC_SIGNAL: 'BTC Signal',
  LAST_UPDATE: 'Last Update',
} as const;

export const CSS_CLASSES = {
  APP_CONTAINER: 'app-container',
  HEADER: 'header',
  TABLE_CONTAINER: 'table-container',
  STATS_INFO: 'stats-info',
  TABLE: 'table',
  SYMBOL_CELL: 'symbol-cell',
  CRYPTO_ICON: 'crypto-icon',
  CHART_MODAL: 'chart-modal',
  CHART_MODAL_CONTENT: 'chart-modal-content',
} as const;

export const DATA_PROCESSING = {
  CRYPTO_ICON_LENGTH: 2,
  DEFAULT_VALUE: '-',
} as const;
